{"_last_used_preset": "Preset: Factures", "Preset: OV LOCAL": {"document_type": "OV", "source_folder": "//************/tarec/Archive/SCANNER/OV LOCAL", "excel_file": "//************/tarec/Archive/Archive 2023/SUIVI OV CHQ OPCVM BC 2023.xlsx", "excel_sheet": "OV", "processed_folder": "//************/tarec/Archive/Archive 2023/OV", "skip_folder": "//************/tarec/Archive/SCANNER/OV LOCAL/SKIPPED", "output_template": "{processed_folder}/{filter1}/OV {filter1} {filter3}.pdf", "filter1_column": "FOURNISSEUR", "filter2_column": "N° OV", "filter3_column": "N° FACTURE", "filter4_column": "MONTANT ", "prompt": "Extract the following information from this payment order:\n1. Supplier/Beneficiary Name\n2. Payment Order Number\n3. Related Invoice Numbers (semicolon-separated if multiple)\n4. Total Amount (with currency)\n\nReturn ONLY a JSON object with these fields as keys:\n{\n    \"supplier_name\": \"extracted supplier/beneficiary name\",\n    \"order_number\": \"extracted payment order number they are generally in this format 'TAREC-OV-2019-05-004'\",\n    \"related_invoices\": \"extracted invoice references, generally specified after 'Motif:'\",\n    \"total_amount\": \"extracted amount with currency\"\n    }\n\nIf any field is not found in the image, set its value to null.", "field_mappings": {"supplier_name": "filter1", "order_number": "filter2", "related_invoices": "filter3", "total_amount": "filter4"}, "vision": {"enabled": true, "gemini_api_key": "AIzaSyDK1h7R1fdjMkT57lZDUXDHOVz3bgohcYo", "model": "gemini-2.0-flash", "supplier_match_threshold": 0.75, "default_language": "fr", "ocr_preprocessing": true}}, "Preset: Bon de Commande": {"document_type": "BC", "source_folder": "C:/Users/<USER>/Documents/Tarec/BoltQT/tests/data/Source/BC/", "processed_folder": "C:/Users/<USER>/Documents/Tarec/BoltQT/tests/data/Dest/", "excel_file": "C:/Users/<USER>/Documents/Tarec/BoltQT/tests/data/SUIVI OV CHQ OPCVM BC 2023 test.xlsx", "excel_sheet": "BON DE COMMANDE", "output_template": "{processed_folder}/{filter3|date.year}/MOIS {filter3|date.month}/{filter2|str.slice:0:2} {filter1}{filter2|str.slice:2:}.pdf", "filter1_column": "FOURNISSEUR", "filter2_column": "N° BC", "filter3_column": "DATE BC", "filter4_column": "MONTANT", "prompt": "Extract the following information from this purchase order:\n1. Supplier/Vendor Name\n2. Purchase Order Number\n3. Purchase Order Date (in format DD/MM/YYYY)\n4. Total Amount (with currency)\n\nReturn ONLY a JSON object with these fields as keys:\n{\n    \"supplier_name\": \"extracted supplier name\",\n    \"order_number\": \"extracted purchase order number, generally in this format 'BC 25-01-0010'\",\n    \"order_date\": \"extracted date in DD/MM/YYYY format\",\n    \"total_amount\": \"extracted amount with currency 'Montant TTC'\"\n    }\n\nIf any field is not found in the image, set its value to null.", "field_mappings": {"supplier_name": "filter1", "order_number": "filter2", "order_date": "filter3", "total_amount": "filter4"}, "vision": {"enabled": true, "gemini_api_key": "AIzaSyDK1h7R1fdjMkT57lZDUXDHOVz3bgohcYo", "model": "gemini-2.0-flash", "supplier_match_threshold": 0.75, "default_language": "fr", "ocr_preprocessing": true}}, "Preset: Factures": {"document_type": "FA", "source_folder": "//************/tarec/Archive/SCANNER/FACTURE RECUES", "excel_file": "//************/tarec/Archive/Archive 2023/SUIVI OV CHQ OPCVM BC 2023.xlsx", "excel_sheet": "FACTURE", "processed_folder": "//************/tarec/Archive/Archive 2023/FACTURE RECU", "skip_folder": "//************/tarec/Archive/SCANNER/FACTURE RECUES/SKIPPED", "output_template": "{processed_folder}/{filter3|date.year}/MOIS {filter3|date.month}/{filter2|str.first_word} {filter1} {filter2|str.split_no_last}.pdf", "filter1_column": "FOURNISSEUR", "filter2_column": "N° FACTURE", "filter3_column": "DATE FACTURE", "filter4_column": "MONTANT", "prompt": "Extract the following information from this scanned invoice:\n1. Supplier/Vendor Name\n2. Invoice Number\n3. Invoice Date (in format DD/MM/YYYY)\n4. Total Amount (without currency and without thousand separators)\n\nReturn ONLY a JSON object with these fields as keys:\n{\n    \"supplier_name\": \"extracted supplier name\",\n    \"invoice_number\": \"extracted invoice number\",\n    \"invoice_date\": \"extracted date in DD/MM/YYYY format\",\n    \"total_amount\": \"extracted amount without currency and without thousand separators\"\n}\n\nIf any field is not found in the image, set its value to null.", "field_mappings": {"supplier_name": "filter1", "order_number": "filter2", "related_invoices": "filter3", "total_amount": "filter4"}, "vision": {"enabled": true, "gemini_api_key": "AIzaSyDK1h7R1fdjMkT57lZDUXDHOVz3bgohcYo", "model": "gemini-2.0-flash", "supplier_match_threshold": 0.75, "default_language": "fr", "ocr_preprocessing": true}}}