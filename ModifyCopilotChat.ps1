# PowerShell script to remove x-onbehalf-extension-id header from GitHub Copilot Chat extension
# IMPORTANT: Modifying extension files is risky and may break functionality or violate terms of service. Proceed with caution.
#            The most reliable way to use this script is to MANUALLY update the $pattern variable below each time if needed in the future.

# Initialize variables
$shouldProceed = $true
$status = ""
$backupPath = "" # Initialize backup path variable
# $useRegex = $false # Default behavior is literal matching. Uncomment and set to $true only if using a regex pattern.

# --- How to Find the Correct Pattern ---
# 1. Locate your VS Code extensions folder (usually C:\Users\<USER>\.vscode\extensions).
# 2. Find the folder starting with 'github.copilot-chat-' (e.g., github.copilot-chat-0.26.3). Use the latest version if multiple exist.
# 3. Inside that folder, navigate to the 'dist' subdirectory (or search recursively within 'dist' if needed).
# 4. Open the 'extension.js' file in a text editor (VS Code, Notepad++, etc.).
# 5. Search (Ctrl+F) for the text: "x-onbehalf-extension-id"
# 6. The code is minified (no spaces/newlines). You need to find the full key-value pair. It will likely look something like:
#    ...,"x-onbehalf-extension-id":`${e}/${t}`,...
#    or similar, where `${e}/${t}` is the value part (variable names like 'e', 't' might differ).
# 7. Carefully copy the EXACT text, STARTING FROM THE COMMA (,) BEFORE "x-onbehalf-extension-id"
#    and ENDING AFTER THE CLOSING BACKTICK (`) or quote that marks the end of the value.
#    Example to copy: ,"x-onbehalf-extension-id":`${e}/${t}`
# 8. Paste this exact copied text inside the single quotes for the $pattern variable below.

# --- Define the Pattern to Remove ---
# Option A: Exact String (Recommended - Update this manually!)
# Corrected pattern based on user's extension.js snippet:
$pattern = ',"x-onbehalf-extension-id":`${n}/${c}`'

# Option B: Regex (More Flexible but Riskier - Use only if Option A fails and you understand Regex)
# If using this, comment out the line above and uncomment the line below AND set $useRegex = $true above.
# $pattern = ',\s*"x-onbehalf-extension-id"\s*:\s*`.*?`' # Regex looking for key and backtick-enclosed value

# --- Script Execution Starts ---

# Step 1: Inform the user and get confirmation
Write-Host "--------------------------------------------------"
Write-Host "Attempting to modify GitHub Copilot Chat extension..."
Write-Host "Purpose: Remove 'x-onbehalf-extension-id' header."
Write-Host "WARNING: Modifying extension files is risky, may break the extension,"
Write-Host "         violate terms of service, or have unintended consequences."
Write-Host "         A backup (.bak) will be created."
Write-Host "--------------------------------------------------"
do {
    $confirm = Read-Host "Do you understand the risks and want to proceed? (Y/N)"
    if ($confirm -ne $null) { $confirm = $confirm.Trim().ToUpper() }
    if ($confirm -eq "N") {
        $status = "Operation cancelled by user at initial prompt."
        $shouldProceed = $false
    } elseif ($confirm -ne "Y") {
        Write-Host "Please enter Y or N."
    }
} while ($confirm -ne "Y" -and $confirm -ne "N")

# Step 2: Locate the extension folder
if ($shouldProceed) {
    $extensionsPath = Join-Path -Path $env:USERPROFILE -ChildPath ".vscode\extensions"
    Write-Host "Searching for extension in: $extensionsPath"
    $copilotChatFolders = Get-ChildItem -Path $extensionsPath -Directory -Filter "github.copilot-chat-*" -ErrorAction SilentlyContinue

    if ($null -eq $copilotChatFolders -or $copilotChatFolders.Count -eq 0) {
        $status = "Error: GitHub Copilot Chat extension folder not found in '$extensionsPath'."
        $shouldProceed = $false
    } else {
        $latestCopilotChatFolder = $copilotChatFolders | Sort-Object Name -Descending | Select-Object -First 1
        $copilotChatFolder = $latestCopilotChatFolder.FullName

        if ($copilotChatFolders.Count -gt 1) {
            Write-Host "Warning: Multiple versions found. Using latest: '$copilotChatFolder'"
        } else {
             Write-Host "Found extension folder: '$copilotChatFolder'"
        }

        # Step 3: Confirm the selected folder
        do {
            $confirm = Read-Host "Is this the correct folder to modify? (Y/N)"
             if ($confirm -ne $null) { $confirm = $confirm.Trim().ToUpper() }
            if ($confirm -eq "N") {
                $status = "Operation cancelled by user at folder confirmation."
                $shouldProceed = $false
            } elseif ($confirm -ne "Y") {
                Write-Host "Please enter Y or N."
            }
        } while ($confirm -ne "Y" -and $confirm -ne "N")
    }
}

# Step 4: Locate the extension.js file
if ($shouldProceed) {
    $distPath = Join-Path -Path $copilotChatFolder -ChildPath "dist"
    $extensionJsPath = Join-Path -Path $distPath -ChildPath "extension.js"

    if (-not (Test-Path $extensionJsPath)) {
        Write-Host "'extension.js' not found directly in 'dist'. Searching subdirectories..."
        # Search recursively within the 'dist' folder specifically
        $foundFiles = Get-ChildItem -Path $distPath -Recurse -Filter "extension.js" -File -ErrorAction SilentlyContinue
        if ($foundFiles.Count -eq 1) {
             $extensionJsPath = $foundFiles[0].FullName
             Write-Host "Found extension.js at: '$extensionJsPath'"
        } elseif ($foundFiles.Count -gt 1) {
            Write-Host "Error: Found multiple 'extension.js' files within '$distPath'. Cannot proceed automatically."
            $foundFiles | ForEach-Object { Write-Host " - $($_.FullName)" }
            $status = "Error: Ambiguous 'extension.js' location."
            $shouldProceed = $false
        } else {
             $status = "Error: 'extension.js' file not found within '$distPath' or its subdirectories."
             $shouldProceed = $false
        }
    }

    # Step 5: Create Backup
    if ($shouldProceed) {
         Write-Host "Using extension file: '$extensionJsPath'"
         $backupPath = "$extensionJsPath.bak"
         try {
             Copy-Item -Path $extensionJsPath -Destination $backupPath -Force -ErrorAction Stop
             Write-Host "Backup created: '$backupPath'"
         } catch {
             $status = "Error: Failed to create backup file at '$backupPath'. Check permissions. Error: $($_.Exception.Message)"
             $shouldProceed = $false
         }
    }

    # Step 6: Final Confirmation Before Modification
    if ($shouldProceed) {
        do {
            $confirm = Read-Host "Ready to modify '$extensionJsPath'? (Last chance to cancel) (Y/N)"
             if ($confirm -ne $null) { $confirm = $confirm.Trim().ToUpper() }
            if ($confirm -eq "N") {
                $status = "Operation cancelled by user before file modification."
                $shouldProceed = $false
            } elseif ($confirm -ne "Y") {
                Write-Host "Please enter Y or N."
            }
        } while ($confirm -ne "Y" -and $confirm -ne "N")
    }
}

# Step 7: Modify the file
if ($shouldProceed) {
    try {
        Write-Host "Reading file content..."
        $content = Get-Content -Path $extensionJsPath -Raw -Encoding UTF8 -ErrorAction Stop

        # Determine matching strategy
        $matchPattern = $pattern
        # Check if $useRegex is defined and explicitly set to $true
        if ($PSBoundParameters.ContainsKey('useRegex') -and $useRegex -eq $true) {
            Write-Host "Using regex pattern matching."
            # Keep $matchPattern as is (it's already the regex pattern)
        } else {
            # Default to literal matching: escape the pattern string
            Write-Host "Using literal string matching."
            $matchPattern = [regex]::Escape($pattern)
        }

        Write-Host "Searching for pattern: $pattern" # Show user the original pattern being used
        # Write-Host "Using match pattern: $matchPattern" # Optional: uncomment to see the escaped pattern

        if ($content -match $matchPattern) {
            Write-Host "Pattern FOUND in the file."
            Write-Host "This pattern will be REMOVED."

            # Use -replace. It handles both literal (when escaped) and regex patterns correctly based on $matchPattern
            $modifiedContent = $content -replace $matchPattern, ''

            # Optional: Verify change (simple check if length decreased)
            if ($modifiedContent.Length -lt $content.Length) {
                 Write-Host "Content modified. Writing changes back to file..."
                 # Overwrite the original file with the modified content
                 # Use -NoNewline to prevent adding an extra newline at the end of the minified file
                 Set-Content -Path $extensionJsPath -Value $modifiedContent -Encoding UTF8 -NoNewline -ErrorAction Stop
                 $status = "Modification successful. Please COMPLETELY close and restart Visual Studio Code for changes to take effect."
            } else {
                 # This case should ideally not happen if -match was true, but good to check.
                 $status = "Error: Pattern was matched, but replacement did not change content length. No changes written."
                 $shouldProceed = $false
            }

        } else {
            $status = "Error: The specified pattern was NOT found in '$extensionJsPath'."
            Write-Host "Please double-check the \$pattern variable in the script against the actual content of the file."
            Write-Host "Remember to include the leading comma and the exact value formatting."
            $shouldProceed = $false
        }
    } catch {
        $status = "Error: An error occurred during file processing: $($_.Exception.Message)"
        $shouldProceed = $false
    }
}

# Final Status Report
Write-Host "--------------------------------------------------"
Write-Host "Script finished."
Write-Host "Status: $status"
Write-Host "--------------------------------------------------"
if ($status -like '*Error*') {
     Write-Host "An error occurred. The original file might be unchanged."
     if (Test-Path $backupPath) {
        Write-Host "You can restore the original file from the backup: $backupPath"
     } else {
        Write-Host "Backup file may not have been created successfully."
     }
} elseif ($status -like '*cancelled*') {
    Write-Host "Operation was cancelled. No changes were made to '$extensionJsPath'."
    if (Test-Path $backupPath) {
         Write-Host "A backup file might exist at: $backupPath"
    }
} elseif ($status -like '*Modification successful*') {
     Write-Host "SUCCESS! Remember to fully restart VS Code."
}

Write-Host "Press Enter to close..."
$null = Read-Host
