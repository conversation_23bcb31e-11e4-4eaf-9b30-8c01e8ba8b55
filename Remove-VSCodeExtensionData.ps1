<#
.SYNOPSIS
Uninstalls the augment.vscode-augment extension and performs a thorough cleanup of associated data on Windows. Targets settings, global storage, extension folder, and optionally workspace storage and caches.

.DESCRIPTION
Specifically designed to help resolve issues like persistent chats or inability to sign out from the augment.vscode-augment extension.
Performs:
1.  Determines VS Code paths (Stable or Insiders).
2.  Prompts the user to ensure VS Code is closed.
3.  Uninstalls augment.vscode-augment via CLI.
4.  Removes the extension's folder from the extensions directory.
5.  Removes the augment.vscode-augment folder from 'globalStorage' (CRITICAL for chat/login data).
6.  Attempts to remove related keys from User 'settings.json'.
7.  If -IncludeWorkspaceStorage is used, attempts interactive cleanup of 'workspaceStorage'.
8.  If -IncludeCache is used, prompts to clear general VS Code cache directories.

.PARAMETER ExtensionId
The full ID of the extension to remove. Pre-filled for 'augment.vscode-augment' but can be overridden.

.PARAMETER Insiders
Switch parameter. Targets the VS Code Insiders installation.

.PARAMETER IncludeWorkspaceStorage
Switch parameter. Attempts interactive cleanup of workspaceStorage. Recommended if globalStorage cleanup isn't enough. USE WITH CAUTION.

.PARAMETER IncludeCache
Switch parameter. Prompts to clear general VS Code cache folders. Optional deeper clean.

.EXAMPLE
# Clean Augment (Stable VS Code), including interactive workspace cleanup
.\Clean-AugmentExtension.ps1 -IncludeWorkspaceStorage -Verbose

.EXAMPLE
# Clean Augment (Insiders VS Code), include workspace AND prompt for cache clearing
.\Clean-AugmentExtension.ps1 -Insiders -IncludeWorkspaceStorage -IncludeCache -Verbose

.EXAMPLE
# Dry run (see what would happen) - HIGHLY RECOMMENDED FIRST
.\Clean-AugmentExtension.ps1 -IncludeWorkspaceStorage -IncludeCache -WhatIf

.NOTES
- CRITICAL: Close ALL VS Code instances before running.
- HIGHLY RECOMMENDED: Backup VS Code data folders.
- Workspace storage cleaning is interactive and potentially risky to UI state of specific workspaces.
- Global Storage cleanup is vital for the Augment sign-out/chat issue.
#>
[CmdletBinding(SupportsShouldProcess = $true, ConfirmImpact = 'High')]
param(
    [Parameter(Mandatory = $false, HelpMessage = "Extension ID to clean. Defaults to augment.vscode-augment")]
    [ValidatePattern('^[a-zA-Z0-9-]+?\.[a-zA-Z0-9-]+$')]
    [string]$ExtensionId = "augment.vscode-augment", # Defaulting to the target extension

    [Parameter(HelpMessage = "Target VS Code Insiders installation")]
    [switch]$Insiders,

    [Parameter(HelpMessage = "Attempt interactive cleanup of workspaceStorage. USE WITH CAUTION.")]
    [switch]$IncludeWorkspaceStorage,

    [Parameter(HelpMessage = "Prompt to clear general VS Code cache folders.")]
    [switch]$IncludeCache
)

# --- Helper Function to Get Paths ---
# (Same Get-VSCodePaths function as in the previous script - snipped for brevity, ensure it's included in your .ps1 file)
function Get-VSCodePaths {
    param([switch]$IsInsiders)

    $appName = if ($IsInsiders) { "Code - Insiders" } else { "Code" }
    $exeName = if ($IsInsiders) { "code-insiders" } else { "code" }
    $profileDirName = if ($IsInsiders) { ".vscode-insiders" } else { ".vscode" }

    $paths = @{
        AppName      = $appName
        ExeName      = $exeName
        ExePath      = $null
        UserDataPath = $null
        StorageJsonPath = $null
        GlobalStoragePath = $null
        WorkspaceStoragePath = $null
        ExtensionsPath = $null
        CachePaths   = @()
        ProfileDir   = Join-Path -Path $env:USERPROFILE -ChildPath $profileDirName
    }

    # Find Executable
    $codeCmd = Get-Command $exeName -ErrorAction SilentlyContinue
    if (-not $codeCmd) {
        Write-Error "Cannot find '$($exeName)' command. Ensure VS Code ($appName) is installed and in PATH."
        return $null
    }
    $paths.ExePath = $codeCmd.Source

    # User Data Paths (AppData)
    $appDataRoot = Join-Path -Path $env:APPDATA -ChildPath $appName
    if (-not (Test-Path $appDataRoot)) {
        Write-Error "Cannot find AppData directory: '$appDataRoot'"
        return $null
    }
    $paths.UserDataPath = Join-Path -Path $appDataRoot -ChildPath "User"
    $paths.GlobalStoragePath = Join-Path -Path $paths.UserDataPath -ChildPath "globalStorage"
    $paths.WorkspaceStoragePath = Join-Path -Path $paths.UserDataPath -ChildPath "workspaceStorage"

     # Storage.json (often one level up from UserDataPath, but check UserDataPath first)
    $paths.StorageJsonPath = Join-Path -Path $paths.UserDataPath -ChildPath "storage.json"
    if (-not (Test-Path $paths.StorageJsonPath)) {
        $paths.StorageJsonPath = Join-Path -Path $appDataRoot -ChildPath "storage.json"
         if (-not (Test-Path $paths.StorageJsonPath)) {
             $paths.StorageJsonPath = $null # Indicate not found
             Write-Verbose "storage.json not found in expected locations."
         }
    }


    # Extensions Path (User Profile)
    $paths.ExtensionsPath = Join-Path -Path $paths.ProfileDir -ChildPath "extensions"

    # Cache Paths (AppData)
    $paths.CachePaths += Join-Path -Path $appDataRoot -ChildPath "Cache"
    $paths.CachePaths += Join-Path -Path $appDataRoot -ChildPath "Code Cache"
    $paths.CachePaths += Join-Path -Path $appDataRoot -ChildPath "GPUCache"

    Write-Verbose "Determined Paths:"
    $paths.GetEnumerator() | ForEach-Object { Write-Verbose "- $($_.Name): $($_.Value)" }

    return $paths
}


# --- Main Script ---
$VerbosePreference = $PSBoundParameters.ContainsKey('Verbose') ? 'Continue' : 'SilentlyContinue'

Write-Host "Starting thorough removal process for Extension ID: $ExtensionId" -ForegroundColor Cyan

# 1. Get Paths
$vsCodePaths = Get-VSCodePaths -IsInsiders:$Insiders.IsPresent
if (-not $vsCodePaths) {
    Exit 1
}

# 2. Prompt User to Close VS Code
Write-Warning ("-" * 70)
Write-Warning "CRITICAL: Ensure ALL instances of VS Code '$($vsCodePaths.AppName)' are CLOSED."
Write-Warning "Check Task Manager (Ctrl+Shift+Esc) for 'Code.exe' processes."
Write-Warning "Failure to do so may corrupt data or prevent cleanup."
Write-Warning ("-" * 70)
Read-Host "Press Enter ONLY when VS Code is fully closed..."

# 3. Uninstall Extension via CLI
Write-Host "[Step 1/6] Attempting to uninstall extension '$ExtensionId' via CLI..."
$uninstallArgs = @("--uninstall-extension", $ExtensionId, "--force")
try {
    if ($PSCmdlet.ShouldProcess($ExtensionId, "Uninstall VS Code Extension via '$($vsCodePaths.ExeName)'")) {
        Write-Verbose "Executing: $($vsCodePaths.ExePath) $($uninstallArgs -join ' ')"
        $process = Start-Process -FilePath $vsCodePaths.ExePath -ArgumentList $uninstallArgs -Wait -PassThru -NoNewWindow -ErrorAction Stop
        if ($process.ExitCode -ne 0) {
            Write-Warning "CLI uninstall command exited with code $($process.ExitCode). Extension might not have been installed or another issue occurred."
        } else {
            Write-Host "CLI uninstall command executed successfully." -ForegroundColor Green
        }
    }
} catch {
    Write-Warning "Warning during CLI uninstall: $_ . Continuing cleanup attempt."
}

# 4. Remove from Extensions Directory (Fallback/Verification)
Write-Host "[Step 2/6] Checking main extensions directory: '$($vsCodePaths.ExtensionsPath)'..."
$foundDirs = Get-ChildItem -Path $vsCodePaths.ExtensionsPath -Directory -Filter "$($ExtensionId.ToLower())-*" -ErrorAction SilentlyContinue
if ($foundDirs) {
    foreach ($dir in $foundDirs) {
        Write-Warning "Found potentially orphaned extension directory: $($dir.FullName)"
         try {
            if ($PSCmdlet.ShouldProcess($dir.FullName, "Remove Extension Directory")) {
                Remove-Item -Path $dir.FullName -Recurse -Force -ErrorAction Stop
                Write-Host "Removed directory: $($dir.FullName)" -ForegroundColor Green
            }
         } catch {
            Write-Error "Error removing directory '$($dir.FullName)': $_"
         }
    }
} else {
    Write-Verbose "No matching extension folder found in $($vsCodePaths.ExtensionsPath) (this is expected if CLI uninstall worked)."
}


# 5. Remove Global Storage Data (CRITICAL FOR AUGMENT STATE)
Write-Host "[Step 3/6] Checking global storage for '$ExtensionId' data (Critical Step)..." -ForegroundColor Yellow
$extensionGlobalStorage = Join-Path -Path $vsCodePaths.GlobalStoragePath -ChildPath $ExtensionId
Write-Verbose "Looking for: $extensionGlobalStorage"

if (Test-Path -Path $extensionGlobalStorage -PathType Container) {
    Write-Warning "Found '$ExtensionId' global storage data (likely contains chats/login): '$extensionGlobalStorage'"
    try {
        if ($PSCmdlet.ShouldProcess($extensionGlobalStorage, "Remove Extension Global Storage (HIGHLY RECOMMENDED)")) {
            Remove-Item -Path $extensionGlobalStorage -Recurse -Force -ErrorAction Stop
            Write-Host "Successfully removed global storage directory: '$extensionGlobalStorage'" -ForegroundColor Green
        }
    } catch {
        Write-Error "Error removing global storage directory '$extensionGlobalStorage': $_"
    }
} else {
    Write-Host "No specific global storage directory found for '$ExtensionId'. If issues persist, check workspace storage next."
}

# 6. Remove User Settings
Write-Host "[Step 4/6] Checking user settings file: '$($vsCodePaths.UserDataPath)\settings.json'..."
$userSettingsPath = Join-Path -Path $vsCodePaths.UserDataPath -ChildPath "settings.json"
if (Test-Path -Path $userSettingsPath -PathType Leaf) {
    # (Using the same robust settings cleaning logic from the previous script)
    # Key searches will include "augment.vscode-augment." and "augment."
    Write-Verbose "Attempting to remove settings for '$ExtensionId' from '$userSettingsPath'"
    $originalContent = Get-Content -Path $userSettingsPath -Raw -ErrorAction SilentlyContinue
    if ($originalContent) {
        $settingsModified = $false
        try {
            # Basic // comment removal attempt
            $contentWithoutComments = ($originalContent -split '\r?\n') | Where-Object { $_.TrimStart() -notlike '// *' } | Out-String
            $settingsObject = $contentWithoutComments | ConvertFrom-Json -ErrorAction Stop

            # Find keys related to the extension (check full ID and potentially the base name)
            $baseName = $ExtensionId.Split('.')[-1] # e.g., "vscode-augment"
            $keysToRemove = $settingsObject.PSObject.Properties | Where-Object { $_.Name -like "$ExtensionId*" -or $_.Name -like "$baseName*" } | Select-Object -ExpandProperty Name

            if ($keysToRemove.Count -gt 0) {
                if ($PSCmdlet.ShouldProcess($userSettingsPath, "Remove settings keys: $($keysToRemove -join ', ')")) {
                    Write-Host "Found $($keysToRemove.Count) potential settings key(s): $($keysToRemove -join ', ')"
                    foreach ($key in $keysToRemove) {
                        if ($settingsObject.PSObject.Properties.Contains($key)) {
                            $settingsObject.PSObject.Properties.Remove($key)
                            $settingsModified = $true
                            Write-Verbose "Marked key '$key' for removal."
                        }
                    }

                    if ($settingsModified) {
                        Write-Host "Saving modified settings (formatting/comments WILL BE LOST)..." -ForegroundColor Yellow
                        $newJson = $settingsObject | ConvertTo-Json -Depth 10 -WarningAction SilentlyContinue
                        ($newJson + [System.Environment]::NewLine) | Set-Content -Path $userSettingsPath -Encoding UTF8 -Force -ErrorAction Stop
                        Write-Host "Successfully updated '$userSettingsPath'." -ForegroundColor Green
                    } else {
                         Write-Host "No matching top-level keys were actually present to remove."
                    }
                } # End ShouldProcess
            } else {
                Write-Verbose "No top-level settings keys found matching '$ExtensionId' or '$baseName'."
            }
        } catch [System.Management.Automation.MethodInvocationException] {
            Write-Warning "Could not parse '$userSettingsPath' as JSON (might have comments). Skipping automatic settings cleanup. Please review manually."
        } catch {
            Write-Error "An error occurred processing '$userSettingsPath': $_. Please review manually."
        }
    } else { Write-Verbose "User settings file '$userSettingsPath' is empty or could not be read." }
} else { Write-Verbose "User settings file '$userSettingsPath' not found." }


# 7. Clean Workspace Storage (Interactive & Optional)
if ($IncludeWorkspaceStorage) {
    Write-Host "[Step 5/6] Attempting Workspace Storage Cleanup (Interactive)..." -ForegroundColor Yellow
    # (Using the same interactive workspace storage cleaning logic from the previous script)
    # Remember this removes state for the *entire workspace*, not just one extension within it.
    Write-Warning "This step cleans storage for specific workspaces you select."
    Write-Warning "It's less likely than Global Storage to hold the Augment login, but possible."
    Write-Warning "Deleting workspace storage resets state (UI, tasks etc.) for that workspace."

    $wsStoragePath = $vsCodePaths.WorkspaceStoragePath
    $storageJson = $vsCodePaths.StorageJsonPath
    $workspaceMap = @{}

    # Try to map storage folders to workspace paths using storage.json
    if ($storageJson -and (Test-Path $storageJson)) {
        Write-Verbose "Parsing '$storageJson' to identify workspaces..."
        try {
            $jsonContent = Get-Content -Path $storageJson -Raw -ErrorAction Stop
             # Basic // comment removal attempt
            $jsonContentClean = ($jsonContent -split '\r?\n') | Where-Object { $_.TrimStart() -notlike '// *' } | Out-String
            $storageData = $jsonContentClean | ConvertFrom-Json -ErrorAction Stop

            # Handle different possible structures in storage.json (adjust based on VS Code version if needed)
             if ($storageData.workspaces) {
                foreach ($key in $storageData.workspaces.PSObject.Properties.Name) {
                   $wsInfo = $storageData.workspaces.$key
                   if ($wsInfo.folder) {
                       $folderHash = Split-Path -Path $key -Leaf
                       $folderPath = [System.Net.WebUtility]::UrlDecode($wsInfo.folder.Replace("file:///", "").Replace("/", "\"))
                       $workspaceMap[$folderHash] = $folderPath
                       Write-Verbose "Mapped storage hash '$folderHash' to workspace '$folderPath'"
                   } elseif ($wsInfo.workspace) { # Handle .code-workspace files
                        $folderHash = Split-Path -Path $key -Leaf
                        $folderPath = [System.Net.WebUtility]::UrlDecode($wsInfo.workspace.Replace("file:///", "").Replace("/", "\"))
                        $workspaceMap[$folderHash] = "$folderPath (Workspace File)"
                        Write-Verbose "Mapped storage hash '$folderHash' to workspace file '$folderPath'"
                   }
                }
            } elseif ($storageData.folders) { # Older structure maybe?
                 foreach($folderInfo in $storageData.folders) {
                     if ($folderInfo.id -and $folderInfo.uri) {
                        $folderPath = [System.Net.WebUtility]::UrlDecode($folderInfo.uri.path.TrimStart('/'))
                        $workspaceMap[$folderInfo.id] = $folderPath
                        Write-Verbose "Mapped storage hash '$($folderInfo.id)' to workspace '$folderPath'"
                     }
                 }
            } else {
                 Write-Verbose "Could not find expected 'workspaces' or 'folders' property in storage.json"
            }


        } catch { Write-Warning "Could not parse '$storageJson'. Workspace names won't be shown. Error: $_" }
    } else { Write-Warning "Could not find '$storageJson'. Workspace names cannot be automatically identified." }

    if (Test-Path $wsStoragePath) {
        $storageFolders = Get-ChildItem -Path $wsStoragePath -Directory -ErrorAction SilentlyContinue
        if ($storageFolders) {
            Write-Host "Found $($storageFolders.Count) workspace storage folders in '$wsStoragePath'."
            Write-Host "Review each prompt. Deleting removes cached state for that entire workspace."

            $confirmAll = $false
            $counter = 0
            foreach ($folder in $storageFolders) {
                $counter++
                $folderName = $folder.Name
                $workspacePathDisplay = if ($workspaceMap.ContainsKey($folderName)) { $workspaceMap[$folderName] } else { "Unknown/Legacy Workspace" }
                $prompt = "($counter/$($storageFolders.Count)) Workspace: '$workspacePathDisplay' `nStorage Folder: '$folderName' `nDelete this workspace storage folder?"

                if ($confirmAll) { $choice = 'Y' }
                else { $choice = Read-Host -Prompt "$prompt (Y=Yes, N=No, A=Yes to All, S=Skip Rest, ?=Help)" }

                switch ($choice.ToUpper()) {
                    'Y' { if ($PSCmdlet.ShouldProcess($folder.FullName, "Remove Workspace Storage")) { try { Remove-Item -Path $folder.FullName -Recurse -Force -ErrorAction Stop; Write-Host "Removed: $($folder.FullName)" -ForegroundColor Green } catch { Write-Error "Error removing '$($folder.FullName)': $_" } } }
                    'A' { $confirmAll = $true; if ($PSCmdlet.ShouldProcess($folder.FullName, "Remove Workspace Storage (All)")) { try { Remove-Item -Path $folder.FullName -Recurse -Force -ErrorAction Stop; Write-Host "Removed: $($folder.FullName)" -ForegroundColor Green } catch { Write-Error "Error removing '$($folder.FullName)': $_" } } }
                    'N' { Write-Host "Skipping: $($folder.FullName)" }
                    'S' { Write-Host "Skipping remaining workspace storage folders."; break } # Exit the loop
                    '?' { Write-Host " Deleting this resets UI state, history, etc for the workspace shown." -ForegroundColor Cyan ; $counter--; Continue } # Re-prompt current
                    default { Write-Host "Invalid choice. Skipping: $($folder.FullName)" }
                }
            }
        } else { Write-Host "No folders found inside '$wsStoragePath'." }
    } else { Write-Host "Workspace storage path '$wsStoragePath' not found." }
    Write-Host "Workspace Storage cleanup finished."

} else {
     Write-Host "[Step 5/6] Skipping Workspace Storage cleanup (use -IncludeWorkspaceStorage parameter to enable)."
}# End If IncludeWorkspaceStorage

# 8. Clean General Caches (Optional)
if ($IncludeCache) {
    Write-Host "[Step 6/6] Clearing General Caches (Optional)..." -ForegroundColor Yellow
    # (Using the same cache cleaning logic from the previous script)
     Write-Warning "This removes temporary files used by VS Code and extensions."
    Write-Warning "Usually doesn't cause data loss but may slow down the next startup."
    $confirmCache = Read-Host "Proceed with clearing general cache folders? (Y/N)"
    if ($confirmCache -eq 'Y') {
        foreach ($cachePath in $vsCodePaths.CachePaths) {
            if (Test-Path $cachePath) {
                Write-Host "Attempting to clear cache: '$cachePath'"
                if ($PSCmdlet.ShouldProcess($cachePath, "Clear Cache Contents")) {
                    try {
                        Get-ChildItem -Path $cachePath -Recurse -Force -ErrorAction SilentlyContinue | Remove-Item -Recurse -Force -ErrorAction Stop
                        Write-Host "Cleared contents of '$cachePath'" -ForegroundColor Green
                    } catch { Write-Error "Error clearing cache '$cachePath': $_" }
                }
            } else { Write-Verbose "Cache path not found: '$cachePath'" }
        }
        Write-Host "General Cache clearing finished."
    } else { Write-Host "Skipping general cache clearing." }

} else {
    Write-Host "[Step 6/6] Skipping General Cache clearing (use -IncludeCache parameter to enable)."
}# End If IncludeCache


# --- Final Summary ---
Write-Host ("-" * 70)
Write-Host "Thorough removal process for '$ExtensionId' completed." -ForegroundColor Cyan
Write-Warning "ACTION: Restart VS Code now."
Write-Warning "If the 'augment.vscode-augment' extension reappears automatically, you might have settings sync enabled and need to remove it there too."
Write-Warning "Check project folders for any leftover 'augment' related files if issues persist."
Write-Host ("-" * 70)